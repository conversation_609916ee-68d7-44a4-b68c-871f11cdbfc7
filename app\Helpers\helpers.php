<?php

use Carbon\Carbon;
use App\Mail\UserAccountMail;
use Illuminate\Support\Facades\Mail;
use SimpleSoftwareIO\QrCode\Facades\QrCode;


function media($file, $path)
{

    // $imageName = time() . '.' . $file->extension();
    // $imageName = time() . '_' . uniqid() . '.webp';

    // Get the original file extension
    $extension = $file->getClientOriginalExtension();

    // Define allowed image extensions
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];

    // If file is an image, rename with `.webp`, otherwise keep original extension
    if (in_array(strtolower($extension), $imageExtensions)) {
        $imageName = time() . '_' . uniqid() . '.webp';
    } else {
        $imageName = time() . '_' . uniqid() . '.' . $extension;
    }

    $file->move(public_path($path), $imageName);
    return $imageName;
}

function QRmedia($data, $path)
{
    // Ensure data isn't too complex
    // if (strlen($data) > 500) {
    //     // Consider truncating or compressing data
    //     $data = substr($data, 0, 500);
    // }
    
    $imageName = time() . '_' . uniqid() . '.png';
    $fullPath = public_path($path . '/' . $imageName);

    // Generate QR Code with better settings for scanning
    $qrCode = QrCode::format('png')
        ->size(300)  // More scannable size
        ->errorCorrection('H')  // High error correction
        ->margin(1)  // Smaller margin
        ->generate($data);

    // Save the QR Code file
    file_put_contents($fullPath, $qrCode);
    
    return $imageName;
}
// function QRmedia($data, $path)
// {
//     $imageName = time() . '_' . uniqid() . '.png';
//     $fullPath = public_path($path . '/' . $imageName);

//     // Generate QR Code as PNG binary data
//     $qrCode = QrCode::format('png')->size(500)->generate($data);

//     // Save the QR Code file
//     file_put_contents($fullPath, $qrCode);

//     return $imageName;
// }

function customDate($date, $format, $to_date = null)
{
    return Carbon::parse($date)->format($format);
}

function sendAccountMail($userCreated, $type, $password,$resetToken)
{

    Mail::to($userCreated->email)->send(new UserAccountMail($userCreated,$type,$password,$resetToken));
}
