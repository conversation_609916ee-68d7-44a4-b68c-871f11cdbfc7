$(document).ready(function () {
    $("#logout_btn").click(function (e) {
        e.preventDefault(); // Prevent the default anchor tag behavior
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/logout";
        let apiUrl = baseURL + apiEndpoint;
        let csrfToken = $('meta[name="csrf-token"]').attr("content"); // Get the CSRF token value

        $.ajax({
            url: apiUrl,
            type: "POST",
            headers: {
                "X-CSRF-TOKEN": csrfToken, // Pass the CSRF token in the headers
            },
            success: function (response) {
                showAlert("you are logout...", "success");
                setTimeout(function () {
                    window.location.href = "/portal/login";
                }, 1000);
            },
            error: function (xhr, status, error) {
                // Handle error
                console.error(xhr);
                showAlert("system error.", "danger");
            },
        });
    });

    // Add event listener to toggle switches
    document.addEventListener("change", function (event) {
        if (event.target.classList.contains("article_category_status")) {
            const articelCategoryId = event.target.getAttribute("data-id");
            const checked = event.target.checked;
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/blog/category/status/update/${articelCategoryId}`;
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request to update status using AJAX
            $.ajax({
                url: apiUrl,
                type: "PUT",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                data: { status: checked },
                success: function (response) {
                    // Show success message or update UI as needed
                    showAlert("status updated !", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    //add business users
    $("#add_user").validate({
        rules: {
            name: {
                required: true,
            },

            email: {
                required: true,
            },

            password: {
                required: true,
            },
            role: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/business/user/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("user added !", "success");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("addUser")
                    );
                    offcanvas.hide();
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "user already exists with the same email.",
                            "warning"
                        );
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("addUser")
                        );
                        offcanvas.hide();
                    }
                },
            });
        },
    });

    //delete business users
    var businessUserId = null;
    // Handle delete button click
    $(".business_user_delete_btn").on("click", function () {
        businessUserId = $(this).data("id");
        $("#businessUserDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#businessUserDeleteModal .delete_record").on("click", function () {
        if (businessUserId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/business/user/delete/${businessUserId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#businessUserDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + businessUserId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("User deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    //update business users status
    document.addEventListener("change", function (event) {
        if (event.target.classList.contains("business_user_status")) {
            const businessUserId = event.target.getAttribute("data-id");
            const checked = event.target.checked;
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/business/user/status/update/${businessUserId}`;
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request to update status using AJAX
            $.ajax({
                url: apiUrl,
                type: "PUT",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                data: { status: checked },
                success: function (response) {
                    // Show success message or update UI as needed
                    showAlert("status updated !", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    //update business users data

    $("#update_business_user").validate({
        rules: {
            name: {
                required: true,
            },

            email: {
                required: true,
            },
            role: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/business/user/update";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("user updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/profile";
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        form.reset();
                        resetDropify();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("user already exists.", "warning");
                        // setTimeout(function () {
                        //     window.location.href = "/dashboard/profile";
                        // }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        // setTimeout(function () {
                        //     window.location.href = "/dashboard/profile";
                        // }, 2000);
                    }
                },
            });
        },
    });

    // upload website images

    $("#upload_web_image").validate({
        rules: {
            web_images: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/dashboard/image/web";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            // Get the form element and target its loader
            var $form = $(form);
            var $formLoader = $form.find("#form_loader");
            $formLoader.removeClass("d-none"); // Show the loader

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    resetDropify();
                    $formLoader.addClass("d-none");

                    var anchorElement = $("<a>", {
                        href: response.location,
                        text: response.location,
                        target: "_blank",
                    });

                    $("#url_link small").html(anchorElement);

                    showAlert("image uploaded added !", "success");
                },
                error: function (xhr, status, error) {
                    form.reset();
                    resetDropify();
                    $formLoader.addClass("d-none");
                    if (xhr.status === 422) {
                        showAlert("system error", "warning");
                    } else {
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    // delete  images/files from gallery

    $(document).on("submit", ".delete_gallery_file", function (e) {
        e.preventDefault();
        if (!confirm("Are you sure you want to delete this image?")) {
            return; // If the user clicks "Cancel", exit the function
        }
        // Get the form and data
        var form = $(this);
        var formData = new FormData(form[0]);

        // Set API endpoint
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/dashboard/file/delete";
        let apiUrl = baseURL + apiEndpoint;

        $.ajax({
            url: apiUrl,
            type: "POST",
            data: formData,
            processData: false, // Important! Don't process the data
            contentType: false, // Important! Set content type to false
            dataType: "json",
            success: function (response) {
                if (response.success) {
                    // Remove the image div from the DOM
                    form.closest(".gallery_single_file").remove();
                    showAlert("Image deleted successfully!", "success");
                } else {
                    showAlert(response.message, "warning");
                }
            },
            error: function (xhr, status, error) {
                showAlert("Error deleting the image. Try again.", "danger");
            },
        });
    });

    $("#web_settings").validate({
        rules: {},

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/dashboard/website/settings";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",

                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("settings updated !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    if (xhr.status === 422) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert("No change in the form data.", "warning");
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "danger"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    }
                },
            });
        },
    });

    // Association Crud request

    $("#add_association").validate({
        rules: {
            // assoc_type: {
            //     required: true,
            // },
            // assoc_name: {
            //     required: true,
            // },
            // assoc_president_name: {
            //     required: true,
            // },
            // assoc_office_address: {
            //     required: true,
            // },
            // assoc_phone_num: {
            //     required: true,
            // },
            // assoc_email: {
            //     required: true,
            // },
            // assoc_password: {
            //     required: true,
            // },
            // assoc_registration_certificate: {
            //     required: true,
            // },
            // assoc_tourism_certificate: {
            //     required: true,
            // },
            // assoc_logo: {
            //     required: true,
            // },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/association/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Association added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/associations";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_association").validate({
        rules: {
            // assoc_type: {
            //     required: true,
            // },
            // assoc_name: {
            //     required: true,
            // },
            // assoc_president_name: {
            //     required: true,
            // },
            // assoc_office_address: {
            //     required: true,
            // },
            // assoc_phone_num: {
            //     required: true,
            // },
            // assoc_email: {
            //     required: true,
            // },
            // assoc_password: {
            //     required: true,
            // },
            // assoc_registration_certificate: {
            //     required: true,
            // },
            // assoc_tourism_certificate: {
            //     required: true,
            // },
            // assoc_logo: {
            //     required: true,
            // },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var associationId = $("#assoc_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/association/update/" + associationId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Association updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/associations";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    //delete association
    var associationId = null;
    // Handle delete button click
    $(".association_delete_btn").on("click", function () {
        associationId = $(this).data("id");
        $("#associationDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#associationDeleteModal .delete_record").on("click", function () {
        if (associationId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/association/delete/${associationId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#associationDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + associationId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert(
                            "Association deleted successfully !",
                            "success"
                        );
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    $("#add_tour_operator").validate({
        rules: {
            name: {
                required: true,
            },
            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },
            email: {
                required: true,
            },
            password: {
                required: true,
            },
            registration_certificate: {
                required: true,
            },
            tourism_certificate: {
                required: true,
            },
            profile_img: {
                required: true,
            },
            tin_num: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/association/operator/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Tour Operator added !", "success");
                    setTimeout(function () {
                        window.location.href =
                            "/dashboard/association/operator";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/association/operator";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_tour_operator").validate({
        rules: {
            name: {
                required: true,
            },
            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },

            registration_certificate: {
                required: true,
            },
            tourism_certificate: {
                required: true,
            },
            profile_img: {
                required: true,
            },
            tin_num: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var assocOperatorId = $("#assoc_operator_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint =
                "/api/association/operator/update/" + assocOperatorId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Association Operator updated !", "success");
                    setTimeout(function () {
                        window.location.href =
                            "/dashboard/association/operator";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/association/operator";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    //delete association operator
    var assocOperatorId = null;
    // Handle delete button click
    $(".assoc_operator_delete_btn").on("click", function () {
        assocOperatorId = $(this).data("id");
        $("#assocOperatorDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#assocOperatorDeleteModal .delete_record").on("click", function () {
        if (assocOperatorId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/association/operator/delete/${assocOperatorId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#assocOperatorDeleteModal").modal("hide");

                    var $row = $(
                        'a[data-id="' + assocOperatorId + '"]'
                    ).closest("tr");
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert(
                            "Association Operator deleted successfully !",
                            "success"
                        );
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/association/operator";
                        }, 1500);
                    } else {
                        // Handle error
                        showAlert(
                            "There is a problem contact supprt !",
                            "error"
                        );
                    }
                },
            });
        }
    });

    $("#add_region").validate({
        rules: {
            name: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/region/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Region added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/regions";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_region").validate({
        rules: {
            name: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var regionId = $("#region_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/region/update/" + regionId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Region updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/regions";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    //delete association operator
    var regionId = null;
    // Handle delete button click
    $(".region_delete_btn").on("click", function () {
        regionId = $(this).data("id");
        $("#regionDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#regionDeleteModal .delete_record").on("click", function () {
        if (regionId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/region/delete/${regionId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#regionDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + regionId + '"]').closest("tr");
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("Region deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    $("#add_regional_agent").validate({
        rules: {
            name: {
                required: true,
            },
            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },
            email: {
                required: true,
            },
            password: {
                required: true,
            },
            profile_img: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/regional/agent/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Agent added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/region/agent";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_regional_agent").validate({
        rules: {
            name: {
                required: true,
            },

            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },
            profile_img: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var regionId = $("#regional_agent_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/regional/agent/update/" + regionId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Agent Data updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/region/agent";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    //delete association operator
    var regionalAgentId = null;
    // Handle delete button click
    $(".regional_agent_delete_btn").on("click", function () {
        regionalAgentId = $(this).data("id");
        $("#regionalAgentDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#regionalAgentDeleteModal .delete_record").on("click", function () {
        if (regionalAgentId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/regional/agent/delete/${regionalAgentId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#regionalAgentDeleteModal").modal("hide");

                    var $row = $(
                        'a[data-id="' + regionalAgentId + '"]'
                    ).closest("tr");
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("Agent deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                },
            });
        }
    });

    $("#add_destination").validate({
        rules: {
            name: {
                required: true,
            },
            entrance_fee: {
                required: true,
            },
            tax: {
                required: true,
            },
            price: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            // Check if checkbox is checked, if yes, send 1 else 0
            var feeTypeStatus = $("#fee_type_status").is(":checked") ? 1 : 0;
            formData.append("fee_type_status", feeTypeStatus);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Destination added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/destinations";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href = "/dashboard/destinations";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_destination").validate({
        rules: {
            name: {
                required: true,
            },
            entrance_fee: {
                required: true,
            },
            tax: {
                required: true,
            },
            price: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var destinationId = $("#destination_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/destination/update/" + destinationId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var feeTypeStatus = $("#fee_type_status").is(":checked") ? 1 : 0;
            formData.append("fee_type_status", feeTypeStatus);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Destination updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/destinations";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href = "/dashboard/destinations";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    let destinationApprovalCheckbox = null;
    let isApprovedConfirmed = false;

    document.addEventListener("change", function (event) {
        if (event.target.classList.contains("destination_aproval_status")) {
            const destinationId = event.target.getAttribute("data-id");
            const checked = event.target.checked;

            if (!checked) {
                showAlert("This destination is already approved!", "info");
                setTimeout(() => {
                    event.target.checked = true;
                }, 100);
                return;
            }

            destinationApprovalCheckbox = event.target;
            isApprovedConfirmed = false; // Reset confirmation flag

            $("#approveDestinationModal").modal("show");

            // Prevent multiple bindings
            $("#approveDestinationModal .approve_record")
                .off("click")
                .on("click", function () {
                    let baseURL = window.location.origin;
                    let apiEndpoint = `/api/destination/status/update/${destinationId}`;
                    let apiUrl = baseURL + apiEndpoint;

                    $.ajax({
                        url: apiUrl,
                        type: "PUT",
                        headers: {
                            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                                "content"
                            ),
                        },
                        data: { status: true },
                        success: function (response) {
                            isApprovedConfirmed = true; // ✅ Mark as confirmed
                            showAlert("Destination approved!", "success");

                            // Hide modal after setting flag
                            $("#approveDestinationModal").modal("hide");
                            setTimeout(function () {
                                window.location.href =
                                    "/dashboard/destinations";
                            }, 1000);
                        },
                        error: function (xhr, status, error) {
                            console.error(xhr);
                            showAlert(
                                "There is a problem, contact support!",
                                "error"
                            );

                            // Revert checkbox if failure
                            if (destinationApprovalCheckbox) {
                                destinationApprovalCheckbox.checked = false;
                            }
                        },
                    });
                });

            // Reset checkbox if modal closed and not approved
            $("#approveDestinationModal")
                .off("hidden.bs.modal")
                .on("hidden.bs.modal", function () {
                    if (!isApprovedConfirmed && destinationApprovalCheckbox) {
                        destinationApprovalCheckbox.checked = false;
                    }

                    // Reset flags
                    isApprovedConfirmed = false;
                    destinationApprovalCheckbox = null;
                });
        }
    });

    //delete association operator
    var destinationId = null;
    // Handle delete button click
    $(".destination_delete_btn").on("click", function () {
        destinationId = $(this).data("id");
        $("#destinationDeleteModal").modal("show");
    });

    // Handle delete confirmation in modal
    $("#destinationDeleteModal .delete_record").on("click", function () {
        if (destinationId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/destination/delete/${destinationId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#destinationDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + destinationId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert(
                            "Destination deleted successfully !",
                            "success"
                        );
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr, status, error) {
                    if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href = "/dashboard/destinations";
                        }, 1500);
                    } else {
                        // Handle error
                        showAlert(
                            "There is a problem contact supprt !",
                            "error"
                        );
                    }
                },
            });
        }
    });

    $("#add_destination_agent").validate({
        rules: {
            name: {
                required: true,
            },
            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },
            email: {
                required: true,
            },
            password: {
                required: true,
            },
            profile_img: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/agent/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            var roleKeyValue = $("#role_key").val();
            formData.append("role_key", roleKeyValue);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Agent added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/destination/agent";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/agent";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_destination_agent").validate({
        rules: {
            name: {
                required: true,
            },

            address: {
                required: true,
            },
            phone_num: {
                required: true,
            },
            profile_img: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var destinationId = $("#destination_agent_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/destination/agent/update/" + destinationId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Agent Data updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/destination/agent";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/agent";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    //delete association operator
    var destinationAgentId = null;
    // Handle delete button click
    $(".destination_agent_delete_btn").on("click", function () {
        destinationAgentId = $(this).data("id");
        $("#destinationAgentDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#destinationAgentDeleteModal .delete_record").on("click", function () {
        if (destinationAgentId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/destination/agent/delete/${destinationAgentId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "DELETE",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#destinationAgentDeleteModal").modal("hide");

                    var $row = $(
                        'a[data-id="' + destinationAgentId + '"]'
                    ).closest("tr");
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert("Agent deleted successfully !", "success");
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/agent";
                        }, 1500);
                    } else {
                        // Handle error
                        showAlert(
                            "There is a problem contact supprt !",
                            "error"
                        );
                    }
                },
            });
        }
    });

    $("#add_departure").validate({
        rules: {
            destinations: {
                required: true,
            },
            total_tourist: {
                required: true,
            },
            adults: {
                required: true,
            },
            children: {
                required: true,
            },
            nationality: {
                required: true,
            },
            start_end_date: {
                required: true,
            },
            group_name: {
                required: true,
            },
            male: {
                required: true,
            },
            female: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/operator/departure/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Departure added !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/operator/departures";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 422) {
                        showAlert("Check the error's", "danger");
                        var errors = xhr.responseJSON;
                        $.each(errors, function (key, value) {
                            showAlert(value, "danger");
                        });
                    } else if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/operator/departures";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $("#update_departure").validate({
        rules: {
            destinations: {
                required: true,
            },
            total_tourist: {
                required: true,
            },
            adults: {
                required: true,
            },
            children: {
                required: true,
            },
            nationality: {
                required: true,
            },
            start_end_date: {
                required: true,
            },
            group_name: {
                required: true,
            },
            male: {
                required: true,
            },
            female: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            var departureId = $("#departure_id").val(); // Get ID from a hidden field or data attribute
            let apiEndpoint = "/api/operator/departure/update/" + departureId;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Departure Data updated !", "success");
                    setTimeout(function () {
                        window.location.href = "/dashboard/operator/departures";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/operator/departures";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    $(".generate_card_btn").on("click", function () {
        departureId = $(this).data("id");
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/operator/departure/generate/${departureId}`;
        let apiUrl = baseURL + apiEndpoint;
        $.ajax({
            url: apiUrl,
            type: "GET",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                if (response.success) {
                    // **Remove previous images before adding a new one**
                    $(".operator_card_body img").remove();
                    // Ensure the operator card container is hidden initially
                    $("#operatorCardContainer").removeClass("d-none");

                    let data = response.data;

                    // Fill the modal with fetched data
                    $("#operatorName").text(data.operatorName);
                    $("#profileImage").attr("src", data.operatorImage);
                    $("#assocName").text(data.assoc_name);
                    $("#totalParticipants").text(data.totalParticipants);
                    $("#male").text(data.male);
                    $("#female").text(data.female);
                    $("#groupName").text(data.groupName);
                    $("#nationality").text(data.nationality);
                    $("#qrCode").attr("src", data.qrCodePath);

                    // Show the modal
                    $("#generateCardViewModal").modal("show");

                    // Convert to Image after modal is shown
                    setTimeout(() => {
                        html2canvas(
                            document.getElementById("operatorCardContainer")
                        ).then(function (canvas) {
                            let imageData = canvas.toDataURL("image/png");

                            // Ensure the operator card container is hidden initially
                            $("#operatorCardContainer").addClass("d-none");

                            // Show the generated image inside modal
                            $(".operator_card_body").append(
                                `<img src="${imageData}" class="img-fluid"/>`
                            );

                            // Set download button
                            $(".download_operator_card")
                                .attr("href", imageData)
                                .attr("download", "operator_card.png");
                        });
                    }, 500);
                }
            },
            error: function (xhr) {
                if (xhr.status == 401) {
                    var errorMessage =
                        xhr.responseJSON.message || "Access denied.";
                    showAlert(errorMessage, "danger");
                    setTimeout(function () {
                        window.location.href = "/dashboard/operator/departures";
                    }, 1500);
                } else {
                    // Handle error
                    showAlert("There is a problem contact supprt !", "error");
                }
            },
        });
    });

    //delete association operator
    var departureId = null;
    // Handle delete button click
    $(".operatorDeparture_delete_btn").on("click", function () {
        departureId = $(this).data("id");
        $("#operatorDepartureDeleteModal").modal("show");
    });
    // Handle delete confirmation in modal
    $("#operatorDepartureDeleteModal .delete_record").on("click", function () {
        if (departureId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/operator/departure/delete/${departureId}`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "POST",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                success: function (response) {
                    // Handle success
                    $("#operatorDepartureDeleteModal").modal("hide");

                    var $row = $('a[data-id="' + departureId + '"]').closest(
                        "tr"
                    );
                    $row.append(
                        '<span class="Deleting_row"> Deleting...</span>'
                    );

                    setTimeout(function () {
                        $row.fadeOut(500, function () {
                            $(this).remove(); // Remove the row from the DOM
                        });
                        showAlert(
                            "Departure deleted successfully !",
                            "success"
                        );
                    }, 1000); // Duration of the CSS transition
                },
                error: function (xhr) {
                    if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/operator/departures";
                        }, 1500);
                    } else if (xhr.status == 400) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/operator/departures";
                        }, 1500);
                    } else {
                        // Handle error
                        showAlert(
                            "There is a problem contact supprt !",
                            "error"
                        );
                    }
                },
            });
        }
    });

    /////// manual payment process requests

    // get operator associated with selectd association
    $("#assoc_selection").on("change", function () {
        var assocId = $(this).val();
        $("#group_size").val("");
        $("#individual_entrance_fee").val("");
        $("#total_entrance_fee").val("");
        $("#group_fee").val("");
        $("#subTotal_amount").val("");
        $("#total_amount_tax").val("");
        $("#total_amount").val("");

        $(".generate_slip").addClass("d-none");

        $("#departure_data").html(
            '<option value="" selected disabled>Select Departure</option>'
        );
        $("#operator_data").html(
            '<option value="" selected disabled>Loading...</option>'
        );
        $(".operator_data .form_process_loader_two").removeClass("d-none");
        if (assocId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/get-operators`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "GET",
                data: { assoc_id: assocId },
                dataType: "json",
                success: function (response) {
                    $(".operator_data .form_process_loader_two").addClass(
                        "d-none"
                    );
                    if (response.status === 200) {
                        let operators = response.data;
                        let options =
                            '<option value="" selected disabled>Select Operator</option>';
                        operators.forEach((operator) => {
                            options += `<option value="${operator.id}">${operator.name}</option>`;
                        });
                        $("#operator_data").html(options);
                    } else {
                        $("#operator_data").html(
                            '<option value="" selected disabled>No operators found</option>'
                        );
                        $("#departure_data").html(
                            '<option value="" selected disabled>Select Departure</option>'
                        );
                    }
                },
                error: function (xhr, status, error) {
                    $(".operator_data .form_process_loader_two").addClass(
                        "d-none"
                    );
                    let errorMsg = "Something went wrong.";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    $("#operator_data").html(
                        `<option value="" selected disabled>${errorMsg}</option>`
                    );
                    $("#departure_data").html(
                        '<option value="" selected disabled>Select Departure</option>'
                    );
                },
            });
        }
    });

    // get departures associated with selectd association
    $("#operator_data").on("change", function () {
        var operatorId = $(this).val();
        $("#group_size").val("");
        $("#individual_entrance_fee").val("");
        $("#total_entrance_fee").val("");
        $("#group_fee").val("");
        $("#subTotal_amount").val("");
        $("#total_amount_tax").val("");
        $("#total_amount").val("");

        $(".generate_slip").addClass("d-none");

        $("#departure_data").html(
            '<option value="" selected disabled>Loading...</option>'
        );
        $(".departure_data .form_process_loader_two").removeClass("d-none");
        if (operatorId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/get-departures`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "GET",
                data: { operator_id: operatorId },
                dataType: "json",
                success: function (response) {
                    $(".departure_data .form_process_loader_two").addClass(
                        "d-none"
                    );
                    if (response.status === 200) {
                        let departures = response.data;
                        let options =
                            '<option value="" selected disabled >Select Departure</option>';
                        departures.forEach((departure) => {
                            options += `<option  value="${departure.id}">${departure.date_range}</option>`;
                        });
                        $("#departure_data").html(options);
                    } else {
                        $("#departure_data").html(
                            '<option value="" selected disabled>No departure found</option>'
                        );
                    }
                },
                error: function (xhr, status, error) {
                    $(".departure_data .form_process_loader_two").addClass(
                        "d-none"
                    );
                    let errorMsg = "Something went wrong.";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    $("#departure_data").html(
                        `<option value="" selected disabled>${errorMsg}</option>`
                    );
                },
            });
        }
    });

    // get departures associated with selectd association
    $("#departure_data").on("change", function () {
        var departureId = $(this).val();
        $(
            ".group_size .form_process_loader_two ,.subTotal_amount .form_process_loader_two"
        ).removeClass("d-none");
        if (departureId) {
            let baseURL = window.location.origin;
            let apiEndpoint = `/api/get-departure-detail`;
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                type: "GET",
                data: { departure_id: departureId },
                dataType: "json",
                success: function (response) {
                    $("#validation_error").empty().removeClass("error_listing");
                    $(
                        ".group_size .form_process_loader_two ,.subTotal_amount .form_process_loader_two"
                    ).addClass("d-none");
                    if (response.status === 200) {
                        let finalData = response.data;
                        let priceRange = finalData.priceRange;
                        // **Set the correct option in the group_price select dropdown**

                        // Check if priceRange is empty
                        if (
                            !priceRange ||
                            Object.keys(priceRange).length === 0
                        ) {
                            // Handle empty priceRange - show N/A

                            // For group_price
                            let group_type_priceData = new Tagify(
                                document.getElementById("group_price")
                            );
                            group_type_priceData.removeAllTags(); // Clear previous values
                            group_type_priceData.addTags(["N/A"]);

                            // For group_type
                            let group_type = new Tagify(
                                document.getElementById("group_type")
                            );
                            group_type.removeAllTags(); // Clear previous values
                            group_type.addTags(["N/A"]);
                        } else {
                            let priceData = Object.values(priceRange).map(
                                (item) => {
                                    return `${item.fee_type} (${item.min_group_size}-${item.max_group_size}): ${item.group_price}`;
                                }
                            );
                            // Assuming Tagify is already initialized on #group_type
                            let group_type_priceData = new Tagify(
                                document.getElementById("group_price")
                            );
                            group_type_priceData.removeAllTags(); // Clear previous values
                            group_type_priceData.addTags(priceData);

                            let group_type_data = Object.values(priceRange).map(
                                (item) => item.fee_type
                            );

                            // Assuming Tagify is already initialized on #group_type
                            let group_type = new Tagify(
                                document.getElementById("group_type")
                            );
                            group_type.removeAllTags(); // Clear previous values
                            group_type.addTags(group_type_data);
                        }

                        $("#group_size").val(finalData.group_size);
                        $("#individual_entrance_fee").val(
                            finalData.individual_entrance_fee
                        );
                        $("#total_entrance_fee").val(
                            finalData.total_entrance_fee
                        );
                        $("#group_fee").val(finalData.group_fee);

                        $("#subTotal_amount").val(finalData.sub_total);
                        $("#total_amount_tax").val(finalData.tax); // Show VAT in two decimal places

                        $("#total_amount").val(finalData.total_price);

                        $(".generate_slip").removeClass("d-none");
                    }
                },
                error: function (xhr, status, error) {
                    $("#group_size").val("");
                    $("#individual_entrance_fee").val("");
                    $("#total_entrance_fee").val("");
                    $("#group_fee").val("");
                    $("#subTotal_amount").val("");
                    $("#total_amount_tax").val("");
                    $("#total_amount").val("");

                    $(".generate_slip").addClass("d-none");

                    $(
                        ".group_size .form_process_loader_two,.subTotal_amount .form_process_loader_two"
                    ).addClass("d-none");
                    let errorMsg = "Something went wrong.";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/payments";
                        }, 1500);
                    } else if (xhr.status == 403) {
                        $("#validation_error")
                            .empty()
                            .addClass("error_listing");

                        // Display error message
                        let errorMessage =
                            xhr.responseJSON.errors ||
                            "This departure do not have the current destination in list";
                        const errorDiv = $(
                            '<div class="error_list"></div>'
                        ).text(errorMessage);
                        $("#validation_error").append(errorDiv);

                        showAlert(errorMessage, "danger");
                    } else {
                        showAlert("system error", "error");
                    }
                },
            });
        }
    });

    // generate payment slip before proessing payment in system
    $("#generate_slip").on("click", function (e) {
        e.preventDefault();
        let serialNumber = $("#serialNumber").val();
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/generate/slip`;
        let apiUrl = baseURL + apiEndpoint;
        let requestData = {
            assoc_id: $("#assoc_selection").val(),
            operator_id: $("#operator_data").val(),
            departure_id: $("#departure_data").val(),
            destination_id: $("#destination_id").val(),
        };
        $(".form_process_loader").removeClass("d-none");
        $.ajax({
            url: apiUrl,
            type: "POST",
            data: JSON.stringify(requestData),
            contentType: "application/json",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                $("#receipt_parent").removeClass("d-none");

                $("#slipSerialNumber").text(serialNumber);
                $("#destination_agent").text(response.destination_agent_name);
                $("#destination_name").text(response.destination_name);
                $("#assoc_name").text(response.assoc_name);
                $("#operator_name").text(response.operator_name);
                $("#tin_num").text(response.tin_num);

                $("#slip_individual_entrance_fee").text(
                    "Birr " + response.individual_entrance_fee
                );
                $("#slip_total_entrance_fee").text(
                    "Birr " + response.total_entrance_fee
                );
                $("#slip_group_size").text(response.group_size);

                // Check if group_fee_type is empty
                if (
                    !response.group_fee_type ||
                    Object.keys(response.group_fee_type).length === 0
                ) {
                    $("#group_fee_type").text("N/A");
                } else {
                    let feeTypes = Object.values(response.group_fee_type)
                        .map((item) => item.fee_type)
                        .join(", ");
                    $("#group_fee_type").text(feeTypes);
                }

                // Check if group_fee is empty
                if (
                    !response.group_fee ||
                    Object.keys(response.group_fee).length === 0
                ) {
                    // Handle empty group_fee - set to N/A

                    $("#group_fee").text("N/A");
                } else {
                    let feeString = Object.entries(response.group_fee)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(", ");
                    $("#group_fee").text(feeString);
                }

                $("#group_name").text(response.group_name);
                $("#nationality").text(response.nationality);

                $("#departure_date").text(response.departure_date);

                $("#subTotalPrice").text("Birr " + response.subTotalPrice);
                $("#taxAmount").text("Birr " + response.tax);
                $("#taxTypeAndPercentage").text(
                    response.tax_type + "(" + response.tax_percentage + "%) :"
                );

                $("#total_price").text("Birr " + response.total_price);

                // Show the modal
                $("#paymentSlipViewModal").modal("show");

                // Convert to Image after modal is shown
                setTimeout(() => {
                    html2canvas(
                        document.getElementById("receipt_print_area")
                    ).then(function (canvas) {
                        let imageData = canvas.toDataURL("image/png");

                        // Show the generated image inside modal
                        $(".receipt_print_area").html(
                            `<img src="${imageData}" class="img-fluid"/>`
                        );

                        // Set download button
                        $(".download_payment_slip")
                            .attr("href", imageData)
                            .attr("download", "payment-slip.png");
                    });
                }, 500);
            },

            error: function (xhr, status, error) {
                $(".form_process_loader").addClass("d-none");
                if (xhr.status == 400) {
                    showAlert("Invalid data provided", "danger");
                } else if (xhr.status == 401) {
                    var errorMessage =
                        xhr.responseJSON.message || "Access denied.";
                    showAlert(errorMessage, "danger");
                    setTimeout(function () {
                        window.location.href =
                            "/dashboard/destination/payments";
                    }, 1500);
                } else {
                    showAlert("System error please check later !", "danger");
                }
            },
        });
    });

    $("#add_destination_departure_payment").validate({
        rules: {
            destination: {
                required: true,
            },
            assoc: {
                required: true,
            },
            operator: {
                required: true,
            },
            departure: {
                required: true,
            },
            size: {
                required: true,
            },
            group_fee: {
                required: true,
            },
            subTotal_amount: {
                required: true,
            },

            total_amount: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/payment/add";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            // Append hidden input manually
            formData.append("destination_id", $("#destination_id").val());
            formData.append("serialNumber", $("#serialNumber").val());

            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("payment completed !", "success");
                    setTimeout(function () {
                        window.location.href =
                            "/dashboard/destination/payments";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");

                    $(".error_list").remove();

                    if (xhr.status == 403) {
                        showAlert("Check the error's above", "danger");
                        var errors = xhr.responseJSON.errors;
                        $("#validation_error").addClass("error_listing");

                        $.each(errors, function (key, value) {
                            const errorDiv = $(
                                '<div class="error_list"></div>'
                            ).text(value);
                            $("#validation_error").append(errorDiv);
                        });
                    } else if (xhr.status == 401) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/payments";
                        }, 1500);
                    } else if (xhr.status == 409) {
                        var errorMessage =
                            xhr.responseJSON.message || "Access denied.";
                        showAlert(errorMessage, "danger");
                        setTimeout(function () {
                            window.location.href =
                                "/dashboard/destination/payments";
                        }, 1500);
                    } else {
                        showAlert(
                            "System error please check later !",
                            "danger"
                        );
                    }
                },
            });
        },
    });

    // generate payment slip after proessing payment in system because prices of destinations may change any time
    $("#generate_slip_record").on("click", function (e) {
        e.preventDefault();
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/generate/slip/record`;
        let apiUrl = baseURL + apiEndpoint;
        let requestData = {
            assoc_id: $("#assoc_selection").val(),
            operator_id: $("#operator_data").val(),
            departure_id: $("#departure_data").val(),
            destination_id: $("#destination_id").val(),
            payment_id: $("#payment_id").val(),
        };
        $(".form_process_loader").removeClass("d-none");
        $.ajax({
            url: apiUrl,
            type: "POST",
            data: JSON.stringify(requestData),
            contentType: "application/json",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                $("#receipt_parent").removeClass("d-none");

                $("#slipSerialNumber").text(response.serial_number);
                $("#date").text(response.created_at);
                $("#destination_agent").text(response.destination_agent_name);
                $("#destination_name").text(response.destination_name);
                $("#assoc_name").text(response.assoc_name);
                $("#operator_name").text(response.operator_name);
                $("#tin_num").text(response.tin_num);

                $("#slip_individual_entrance_fee").text(
                    "Birr " + response.individual_entrance_fee
                );
                $("#slip_total_entrance_fee").text(
                    "Birr " + response.total_entrance_fee
                );
                $("#slip_group_size").text(response.group_size);

                let groupFeeData = JSON.parse(response.group_fee); // decode JSON string

                // Check if groupFeeData is empty
                if (!groupFeeData || Object.keys(groupFeeData).length === 0) {
                    // Handle empty groupFeeData - show N/A
                    $("#group_fee_type").text("N/A");
                    $("#group_fee").text("N/A");
                } else {
                    // Handle non-empty groupFeeData - your existing logic
                    let group_fee_type = Object.keys(groupFeeData)
                        .map((key) => key)
                        .join(", ");
                    $("#group_fee_type").text(group_fee_type);

                    let feeString = Object.entries(groupFeeData)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(", ");

                    $("#group_fee").text(feeString);
                }

                $("#group_name").text(response.group_name);
                $("#nationality").text(response.nationality);

                $("#departure_date").text(response.departure_date);

                $("#subTotalPrice").text("Birr " + response.subTotalPrice);
                $("#taxAmount").text("Birr " + response.tax);
                $("#taxTypeAndPercentage").text(
                    response.tax_type + "(" + response.tax_percentage + "%) :"
                );

                $("#total_price").text("Birr " + response.total_price);

                // Show the modal
                $("#paymentSlipViewModal").modal("show");

                // Convert to Image after modal is shown
                setTimeout(() => {
                    html2canvas(
                        document.getElementById("receipt_print_area")
                    ).then(function (canvas) {
                        let imageData = canvas.toDataURL("image/png");

                        // Show the generated image inside modal
                        $(".receipt_print_area").html(
                            `<img src="${imageData}" class="img-fluid"/>`
                        );

                        // Set download button
                        $(".download_payment_slip")
                            .attr("href", imageData)
                            .attr("download", "payment-slip.png");
                    });
                }, 500);
            },

            error: function (xhr, status, error) {
                $(".form_process_loader").addClass("d-none");
                if (xhr.status == 400) {
                    showAlert("Invalid data provided", "danger");
                } else if (xhr.status == 401) {
                    var errorMessage =
                        xhr.responseJSON.message || "Access denied.";
                    showAlert(errorMessage, "danger");
                    setTimeout(function () {
                        window.location.href =
                            "/dashboard/destination/payments";
                    }, 1500);
                } else {
                    showAlert("System error please check later !", "danger");
                }
            },
        });
    });
});
