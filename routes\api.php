<?php
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\Authenticate;
use App\Http\Controllers\Api\authController;
use App\Http\Controllers\Api\businessApiController;
use App\Http\Controllers\Api\globalController;
use App\Http\Controllers\Api\associationController;
use App\Http\Controllers\Api\operatorController;
use App\Http\Controllers\Api\regionController;
use App\Http\Controllers\Api\regionalAgentController;
use App\Http\Controllers\Api\destinationController;
use App\Http\Controllers\Api\departureController;
use App\Http\Controllers\Api\paymentController;
use App\Http\Controllers\Api\chartController;
use App\Http\Middleware\userRoleCheck;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// Protected route example
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
// Mobile routes have been moved to routes/mobile.php

//portal login and signup
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/update/password', [AuthController::class, 'updatePassword']);
Route::post('/logout', [AuthController::class, 'logout']);

Route::get('/chart/data', [ChartController::class, 'dashboardGraphData']);


Route::get('/get/departures', [departureController::class, 'departuresList']);
Route::get('/get/payments', [paymentController::class, 'paymentsList']);
Route::get('/get/destination', [destinationController::class, 'getDestinationRecord']);
Route::post('/payment/add', [paymentController::class, 'destinationPayment']);

// for admin
Route::get('/filtered-expenses', [ChartController::class, 'getFilteredExpenses']);
Route::get('/get/{id}/destinations', [ChartController::class, 'getDestinations']);
Route::get('/get/{id}/operators', [ChartController::class, 'getAssocOpeators']);
Route::get('/get/{id}/departures', [ChartController::class, 'getOperatorDepartures']);
///end

// for assoc admin
Route::get('/assoc-filtered-expenses', [ChartController::class, 'getAssocFilteredExpenses']);
///end
// for tour operator admin
Route::get('/tourOperator-filtered-expenses', [ChartController::class, 'getTourOperatorFilteredExpenses']);
///end



Route::post('/dashboard/image/web', [globalController::class, 'uploader']);
Route::delete('/dashboard/file/delete', [globalController::class, 'deleteFile']);

//get files from the gallery
Route::get('/dashboard/gallery/files', [globalController::class, 'galleryView']);

// website settings
Route::post('/dashboard/website/settings', [globalController::class, 'webSettings']);



Route::middleware([Authenticate::class, userRoleCheck::class . ':super_admin'])->group(function () {
    //business user managment routes
  //  Route::post('/business/user/add', [businessApiController::class, 'addBusinessUser']);
  //  Route::Delete('/business/user/delete/{id}', [businessApiController::class, 'deleteBusinessUser']);
    Route::put('/business/user/status/update/{id}', [businessApiController::class, 'businessUserStatus']);
    Route::post('/business/user/update', [businessApiController::class, 'businessUserUpdate']);
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':admin,super_admin'])->group(function () {
    //region managment routes
    Route::post('/region/add', [regionController::class, 'storeRegion']);
    Route::post('/region/update/{id}', [regionController::class, 'updateRegion']);
 //   Route::Delete('/region/delete/{id}', [regionController::class, 'deleteRegion']);

    //regional agents managment routes
    Route::post('/regional/agent/add', [regionalAgentController::class, 'storeRegionalAgent']);
    Route::post('/regional/agent/update/{id}', [regionalAgentController::class, 'updateRegionalAgent']);
 //   Route::Delete('/regional/agent/delete/{id}', [regionalAgentController::class, 'deleteRegionalAgent']);

    //association managment routes
    Route::post('/association/add', [associationController::class, 'storeAssociation']);
    Route::post('/association/update/{id}', [associationController::class, 'updateAssociation']);
   // Route::Delete('/association/delete/{id}', [associationController::class, 'deleteAssociation']);

 //destination Status approval routes
   Route::put('/destination/status/update/{id}', [destinationController::class, 'destinationStatus']);
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':regional_agent'])->group(function () {
    //regional destinations managment routes
    Route::post('/destination/add', [destinationController::class, 'storeDestination']);
    Route::post('/destination/update/{id}', [destinationController::class, 'updateDestination']);
   // Route::Delete('/destination/delete/{id}', [destinationController::class, 'deleteDestination']);

    //destinations agents managment routes
    Route::post('/destination/agent/add', [destinationController::class, 'storeDestinationAgent']);
    Route::post('/destination/agent/update/{id}', [destinationController::class, 'updateDestinationAgent']);
//    Route::Delete('/destination/agent/delete/{id}', [destinationController::class, 'deleteDestinationAgent']);
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':association'])->group(function () {
    //association tour operator managment routes
    Route::post('/association/operator/add', [operatorController::class, 'storeOperator']);
    Route::post('/association/operator/update/{id}', [operatorController::class, 'updateOperator']);
  //  Route::Delete('/association/operator/delete/{id}', [operatorController::class, 'deleteOperator']);
});

Route::middleware([Authenticate::class, userRoleCheck::class . ':tour_operator'])->group(function () {
    //departure managment routes
    Route::post('/operator/departure/add', [departureController::class, 'storeDeparture']);
    Route::post('/operator/departure/delete/{id}', [departureController::class, 'departureDelete']);
    Route::post('/operator/departure/update/{id}', [departureController::class, 'departureUpdate']);

});
Route::get('/operator/departure/generate/{id}', [departureController::class, 'generateCard']);


Route::middleware([Authenticate::class, userRoleCheck::class . ':destination_agent,tour_operator'])->group(function () {

    // generate payment slip before proessing payment in system
    Route::post('/generate/slip', [paymentController::class, 'generatePaymentSlip']);

     // generate payment slip after proessing payment in system because prices of destinations may change any time
    Route::post('/generate/slip/record', [paymentController::class, 'generatePaymentRecord']);
});


Route::middleware([Authenticate::class, userRoleCheck::class . ':destination_agent'])->group(function () {
    //departure payments managment routes
    // manuall payment routes
    Route::get('/get-operators', [paymentController::class, 'getOperators']);
    Route::get('/get-departures', [paymentController::class, 'getDepartures']);
    Route::get('/get-departure-detail', [paymentController::class, 'getDepartureDetail']);
    
});
