<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Destinations;
use App\Models\DestinationAgents;
use App\Models\User;
use App\Models\Roles;
use App\Models\DestinationPrices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;


class destinationController extends Controller
{

    public function getDestinationRecord(Request $request)
    {
        $userId = $request->query('user_id');

        $user = User::find($userId);

        if (!$user || !$user->destinationAgent) {
            return response()->json(['error' => 'Destination agent not found for this user id.'], 401);
        }

        $destinationAgent = $user->destinationAgent;
        // Fetch payments with related departure and tour operator
        $payments = Destinations::where('id', $destinationAgent->destination_id)
            ->with(['prices'])
            ->get();
  
        return response()->json([
            'destinationRecord' =>$payments
        ]);
    }

    public function storeDestination(Request $request)
    {

        $user = Auth::user();
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:destinations,name,',
            'entrance_fee' => 'required',
            'tax' => 'required',
            'prices' => 'required|array',

        ]);
        // Conditionally add rules if fee_type_status == 1
        if ($request->fee_type_status == '1') {
            $validator->addRules([
                'prices.*.fee_type' => 'required',
                'prices.*.min_group_size' => 'required|integer|min:1',
                'prices.*.max_group_size' => 'required|integer|min:1',
                'prices.*.group_price' => 'required|numeric|min:1',
            ]);
        }

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
     
        // Create the association record
        $destination = Destinations::create([
            'region_id' => $user->agentRegion->region_id,
            'tax_id' => $request->tax,
            'name' => $request->name,
            'entrance_fee' => $request->entrance_fee,
            'entrance_fee' => $request->entrance_fee,
            // 'fee_type' => $request->fee_type,
            'fee_type_status' => $request->fee_type_status,
            'status' => 0,
        ]);
        if ($request->fee_type_status == '1') {
            foreach ($request->prices as $price) {
                DestinationPrices::create([
                    'destination_id' => $destination->id,
                    'fee_type' => $price['fee_type'],
                    'min_group_size' => $price['min_group_size'],
                    'max_group_size' => $price['max_group_size'],
                    'group_price' => $price['group_price'],
                ]);
            }
        }
        return response()->json(['message' => 'Destination created successfully!', "status" => "success"], 200);
    }

    public function updateDestination(Request $request, $id)
    {
        // Find the operator by ID
        $destination = Destinations::findOrFail($id);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('destinations', 'name')->ignore($id)
            ],
            'entrance_fee' => 'required',
            'tax' => 'required',

        ]);
        // Conditionally add rules if fee_type_status == 1
        if ($request->fee_type_status == '1') {
            $validator->addRules([
               'prices.*.fee_type' => 'required',
                'prices' => 'required|array',
                'prices.*.min_group_size' => 'required|integer|min:1',
                'prices.*.max_group_size' => 'required|integer|min:1',
                'prices.*.group_price' => 'required|numeric|min:1',
            ]);
        }


        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }
        $destination->fee_type_status = $request->fee_type_status;
        $destination->name = $request->name;
        $destination->entrance_fee = $request->entrance_fee;

        // $destination->fee_type = $request->fee_type;

        $destination->tax_id = $request->tax;
        $destination->save();


        // Step 1: Get all existing price IDs for this destination
        $existingPriceIds = $destination->prices()->pluck('id')->toArray();

        // Step 2: Get all incoming price IDs from the request
        $submittedPriceIds = collect($request->prices)
            ->filter(function ($value, $key) {
                return !str_contains($key, 'new'); // Ignore 'new' entries
            })
            ->keys()
            ->map(function ($key) {
                return (int) $key;
            })
            ->toArray();

        // Step 3: Find deleted IDs
        $priceIdsToDelete = array_diff($existingPriceIds, $submittedPriceIds);

        // Step 4: Delete them
        if (!empty($priceIdsToDelete)) {
            DestinationPrices::whereIn('id', $priceIdsToDelete)->delete();
        }

        if ($request->prices) {
            // Update existing prices
            foreach ($request->prices as $price_id => $price_data) {
                if ($price_id !== "new") {
                    DestinationPrices::where('id', $price_id)
                        ->where('destination_id', $destination->id)
                        ->update([
                            'fee_type' => $price_data['fee_type'],
                            'min_group_size' => $price_data['min_group_size'],
                            'max_group_size' => $price_data['max_group_size'],
                            'group_price' => $price_data['group_price'],
                        ]);
                }
            }

            // Insert new prices
            foreach ($request->prices as $price_id => $price_data) {
                if (str_contains($price_id, "new")) {
                    DestinationPrices::create([
                        'fee_type' => $price_data['fee_type'],
                        'destination_id' => $destination->id,
                        'min_group_size' => $price_data['min_group_size'],
                        'max_group_size' => $price_data['max_group_size'],
                        'group_price' => $price_data['group_price'],
                    ]);
                }
            }
        }


        return response()->json(['message' => 'Destination updated successfully!', "status" => "success"], 200);
    }
    public function destinationStatus($id)
    {
        $destination = Destinations::findOrFail($id);

        if (!$destination->status) {
            // Only activate if currently inactive
            $destination->status = 1;
            $destination->save();

            return response()->json(["message" => "destination approved", "status" => "success"], 200);
        }

        // If already active, do not deactivate
        return response()->json(["message" => "destination is already approved", "status" => "info"], 200);
    }

    public function deleteDestination($id)
    {
        // Find the association by ID
        $destination = Destinations::findOrFail($id);
        $destination->delete();

        return response()->json(['message' => 'Destination deleted successfully!', "status" => "success"], 200);
    }


    /////////

    public function storeDestinationAgent(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'phone_num' => 'required|string|max:20',
            'email' => 'required|email',
            'password' => 'required|min:6',
            'destination' => 'required|exists:destinations,id',
            'profile_img' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Check if the destination is already assigned to another agent
        $existingAgent = DestinationAgents::where('destination_id', $request->destination)->first();
        if ($existingAgent) {
            return response()->json(['error' => 'This destination is already assigned to another agent.'], 422);
        }

        $existingUser = User::where('email', $request->email)->first();
        $role = Roles::where('role_key', $request->role_key)->first();

        if ($existingUser) {
            return response()->json(['error' => 'Email already taken Use another email.'], 422);
        }

        // Create the association record
        $agent = DestinationAgents::create([
            'destination_id' => $request->destination,
            'name' => $request->name,
            'address' => $request->address,
            'phone_num' => $request->phone_num,
            'email' => $request->email,
            'profile_img' => $request->profile_img,
        ]);

        if ($agent) {
            $resetToken = Str::random(60);
            // Create the user
            $userCreated = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'destination_agent_id' => $agent->id,
                'role_id' => $role->id,
                'status' => 1,
            ]);
        }
        if ($userCreated) {
            // Send Email to the New User
            sendAccountMail($userCreated, 'destination user', $request->password, $resetToken);
        }
        return response()->json(['message' => 'Agent created successfully!', "status" => "success"], 200);
    }

    public function updateDestinationAgent(Request $request, $id)
    {

        // Find the operator by ID
        $agent = DestinationAgents::findOrFail($id);
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address' => 'required|string',
            'phone_num' => 'required|string|max:20',
            'profile_img' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Update operator data
        $agent->name = $request->name;
        $agent->address = $request->address;
        $agent->phone_num = $request->phone_num;
        $agent->profile_img = $request->profile_img;

        $agent->save();

        return response()->json(['message' => 'agent updated successfully!', "status" => "success"], 200);
    }

    public function deleteDestinationAgent($id)
    {

        $agent = DestinationAgents::findOrFail($id);
        $agent->delete();

        return response()->json(['message' => 'agent deleted successfully!', "status" => "success"], 200);
    }
}
