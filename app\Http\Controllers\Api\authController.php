<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Businesses;
use App\Models\Roles;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class authController extends Controller
{

    public function register(Request $request)
    {

        // Validate the input data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|string|email|unique:users,email', // Unique check for email
            'business_name' => 'required|string|unique:businesses,business_name', // Unique check for business name
            'password' => 'required|string|confirmed',
        ]);

        // If validation fails, return errors
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check if the business already exists
        $existingBusiness = Businesses::first();

        if ($existingBusiness) {
            return response()->json(['error' => 'A business already exists. Please rmove business first.'], 422);
        }

        // Create the business and link it to the user

        Businesses::create([
            'business_name' => $request->business_name,
            'business_logo' => json_encode([
                'logo' => '',
                'favicon' => ''
            ]),
        ]);
        // Find the Business Owner role
        $role = Roles::where('role_key', 'admin')->first();

        // if (!$role) {
        //     // Create the Business Owner role if it does not exist
        //     $role = Roles::create([
        //         'role_key' => 'business_owner',
        //         'role' => 'Admin',
        //     ]);
        // }

        // Check if the email already exists
        $existingUser = User::where('role_id', $role->id)->where('email', $request->email)->first();

        if ($existingUser) {
            return response()->json(['error' => 'Email with admin role is already exists.'], 422);
        }

        // Optionally regenerate token for security
        $newToken = Str::random(60);
        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role_id' => $role->id,
            'status' => 1,
            'remember_token' => $newToken,
        ]);


        Auth::login($user);

        return response()->json(["message" => "Successfully signed up", "status" => "success"]);
    }
  

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $credentials = $validator->validated();

        $user = User::where('email', $request->email)->firstOrFail();
        if (!$user) {
            return response()->json(['errors' => ['email' => ['User does not exist']]], 404);
        }
        if (!Auth::attempt($credentials)) {
            return response()->json(['errors' => ['password' => ['Incorrect password']]], 401);
        }

        // Handle invalid roles
        if ($user->status != 1) {
            Auth::logout(); // Log out the user if their role is not authorized
            return response()->json(['error' => 'Invalid user access'], 402);
        }

        $request->session()->regenerate();

        return response()->json([
            'message' => 'Login successful',
        ], 200);
    }

    public function updatePassword(Request $request)
    {

        $request->validate([
            'new_password' => 'required|min:6',
            'token' => 'required|exists:users,password_reset_token',
        ]);

        $user = User::where('password_reset_token', $request->token)->first();

        if (!$user) {
            return response()->json(['error' => 'Invalid token'], 401);
        }

        // Handle invalid roles
        if ($user->status != 1) {
            Auth::logout(); // Log out the user if their role is not authorized
            return response()->json(['error' => 'Invalid user access'], 402);
        }

        // Update Password
        $user->update([
            'password' => Hash::make($request->new_password),
            // 'password_reset_token' => null, // Clear token after use
        ]);

        Auth::logout();

        // Only invalidate and regenerate the session if it's available (web request)
        if ($request->hasSession()) {
            $request->session()->invalidate();
            $request->session()->regenerateToken();
        }

        return response()->json(['message' => 'Password reset successfull'], 200);
    }


    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json(['message' => 'Logged out successfully'], 200);
    }

    public function mobileApplogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        $credentials = $validator->validated();

        $user = User::where('email', $request->email)->firstOrFail();
        if (!$user) {
            return response()->json(['errors' => ['email' => ['User does not exist']]], 403);
        }
        if (!Auth::attempt($credentials)) {
            return response()->json(['errors' => ['password' => ['Incorrect credentials']]], 401);
        }

        // Handle invalid roles
        if ($user->status != 1) {
            Auth::logout(); // Log out the user if their role is not authorized
            return response()->json(['error' => 'Invalid user access'], 402);
        }
        $token = $user->createToken('App Token')->plainTextToken;

        return response()->json([
            'message' => 'Login successful',

            'token' => $token,  // Return the token to the mobile app
            'user' => [
                'id' => $user->id,
                'role' => $user->role,
                'name' => $user->name,
                'email' => $user->email,
            ]
        ], 200);
    }
    public function appLogout(Request $request)
    {
        // Revoke the current token that was used for the request
        $request->user()->currentAccessToken()->delete();

        return response()->json(['message' => 'Logged out successfully'], 200);
    }
}

