<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Regions;
use App\Models\Payments;
use App\Models\Departures;
use App\Models\User;
use App\Models\Destinations;
use App\Models\TourOperators;
use App\Models\DestinationPrices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use SimpleSoftwareIO\QrCode\Facades\QrCode;


class departureController extends Controller
{
    // operator Departures View routes
    public function departuresList(Request $request)
    {
        $userId = $request->query('user_id');

        $user = User::find($userId);

        if (!$user || !$user->tourOperator) {
            return response()->json(['error' => 'Tour operator not found for this user id.'], 404);
        }

        $departures = Departures::where('tour_operator_id', $user->tourOperator->id)->get();

        // Decode destinations column for each departure and collect unique IDs
        $destinationIds = $departures->map(function ($departure) {
            return json_decode($departure->destinations, true) ?? [];
        })->flatten()->unique()->toArray();

        // Fetch destinations names indexed by ID and convert to an array
        $destinations = Destinations::whereIn('id', $destinationIds)->pluck('name', 'id')->toArray();

        return response()->json([
            'departures' => $departures,
            'destinations' => $destinations
        ]);
    }




    public function storeDeparture(Request $request)
    {
        $user = Auth::user();

        // Validate the request
        $validator = Validator::make($request->all(), [
            'destinations' => 'required',
            'total_tourist' => 'required',
            'adults' => 'required|integer|min:0',
            'children' => 'required|integer|min:0',
            'start_end_date' => 'required',
            'group_name' => 'required',
            'male' => 'required|integer|min:0',
            'female' => 'required|integer|min:0',
            'nationality' => 'required',
        ]);




        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Check if total_tourist equals adults + children
        if ($request->total_tourist != ($request->adults + $request->children)) {
            return response()->json([
                'errors' => ['total_tourist' => 'Total tourist count must match the sum of adults and children.']
            ], 403);
        }

        // Check if total_tourist equals adults + children
        if ($request->total_tourist != ($request->male + $request->female)) {
            return response()->json([
                'errors' => ['total_tourist' => 'Total tourist count must match the sum of male and female.']
            ], 403);
        }

        // Create the association record
        $departure = Departures::create([
            'tour_operator_id' => $user->tour_operator_id,
            'destinations' => json_encode($request->destinations), // Store as JSON
            'total_tourist' => $request->total_tourist,
            'adults' => $request->adults,
            'children' => $request->children,
            'date_range' => $request->start_end_date,
            'group_name' => $request->group_name,
            'male' => $request->male,
            'female' => $request->female,
            'nationality' => $request->nationality,

        ]);


        return response()->json(['message' => 'Departure created successfully!', "status" => "success"], 200);
    }

    public function departureUpdate(Request $request, $id)
    {
        // Find the departure by ID
        $departure = Departures::findOrFail($id);

        // Validate the request
        $validator = Validator::make($request->all(), [
            'destinations' => 'required',
            'total_tourist' => 'required',
            'adults' => 'required|integer|min:0',
            'children' => 'required|integer|min:0',
            'start_end_date' => 'required',
            'group_name' => 'required',
            'male' => 'required|integer|min:0',
            'female' => 'required|integer|min:0',
            'nationality' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Check if total_tourist equals adults + children
        if ($request->total_tourist != ($request->adults + $request->children)) {
            return response()->json([
                'errors' => ['total_tourist' => 'Total tourist count must match the sum of adults and children.']
            ], 403);
        }

        // Check if total_tourist equals adults + children
        if ($request->total_tourist != ($request->male + $request->female)) {
            return response()->json([
                'errors' => ['total_tourist' => 'Total tourist count must match the sum of male and female.']
            ], 403);
        }


        $departure->destinations = $request->destinations;
        $departure->total_tourist = $request->total_tourist;
        $departure->adults = $request->adults;
        $departure->children = $request->children;
        $departure->date_range = $request->start_end_date;
        $departure->group_name = $request->group_name;
        $departure->male = $request->male;
        $departure->female = $request->female;
        $departure->nationality = $request->nationality;
        $departure->save();

        return response()->json(['message' => 'Departure updated successfully!', "status" => "success"], 200);
    }

    public function departureDelete($id)
    {
        // Check if any payments exist for the departure
        $paymentExists = Payments::where('departure_id', $id)->exists();

        if ($paymentExists) {
            return response()->json([
                'status' => 'error',
                'message' => 'Departure cannot be deleted because it has related payment records.'
            ], 400); // Use HTTP status 400 (Bad Request)
        }

        // Find the association by ID
        $departure = Departures::findOrFail($id);
        // Delete related Payments
        //Payments::where('departure_id', $id)->delete();
        $departure->delete();

        return response()->json(['message' => 'departure deleted successfully!', "status" => "success"], 200);
    }

    public function generateCard($id)
    {
        // Find the departure with the tour operator relation
        $departure = Departures::with('tourOperator')->findOrFail($id);

        // Check if departure exists
        if (!$departure) {
            return response()->json(['error' => 'Departure not found'], 403);
        }

        // Decode the destination IDs stored as JSON
        $destinationIds = json_decode($departure->destinations, true);

        // Get destination details (id, name)
        $destinations = Destinations::whereIn('id', $destinationIds)->get(['id', 'name']);

        // Get tour operator details
        $operator = $departure->tourOperator;

        // Get associated details (assuming a relation exists)
        $association = $operator->association ?? null;


        // Prepare the full JSON data
        $departureData = [
            'departure' => [
                'id' => $departure->id,
                'destinations' => $departure->destinations,
                'total_tourist' => $departure->total_tourist,
                'date_range' => $departure->date_range,
                'status' => $departure->status,
            ],
            'operator' =>[
                'id' => $operator->id,
                'operator_name' => $operator->name,
             ],
             'association' => [
                'id' => $association->id,
                'name' => $association->name,
            ],

        ];
       

        // Check if a QR code already exists and delete the old one
        if ($departure->qrCode) {

            $oldQrPath = public_path('storage/QRcode/' . basename($departure->qrCode));
            if (file_exists($oldQrPath)) {
                unlink($oldQrPath); // Delete the old QR code
            }
        }

        // Generate and save new QR code
        $qrPath = QRmedia(json_encode($departureData), 'storage/QRcode');
        $newQrUrl = asset('storage/QRcode/' . $qrPath);

        // Update the database with the new QR code URL
        $departure->update(['qrCode' => $newQrUrl]);


        // Prepare Data for Frontend
        $data = [
            'operatorName' => $departure->tourOperator->name ?? 'N/A',
            'operatorImage' => $departure->tourOperator->profile_img ?? 'N/A',
            'assoc_name' => $association->name ?? 'N/A',
            'totalParticipants' => $departure->total_tourist,
            'male' => $departure->male ?? 'N/A',
            'female' => $departure->female ?? 'N/A',
            'groupName' => $departure->group_name ?? 'N/A',
            'nationality' => $departure->nationality ?? 'N/A',
            'qrCodePath' => $newQrUrl,
        ];


        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }
}
