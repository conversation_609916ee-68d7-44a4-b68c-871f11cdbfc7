@extends('dashboard.include.layout')
@section('title', 'Update Payment')
@section('content')

@php
$user = auth()->user();
@endphp
<div class="section_header d-flex flex-column">
    <div class="d-flex w-100 align-items-center justify-content-between">
        <!-- <h3>Add Payment</h3> -->
        <div class="generate_slip form_button d-flex"><button id="generate_slip_record"> {{$user && $user->role && ($user->role->role_key === 'destination_agent') ? 'Generate Slip' : 'Print Slip' }}</button></div>
    </div>
    <div id="validation_error" class="mt-2"></div>
</div>

<form method="POST" enctype="multipart/form-data">
    @csrf
    <input type="hidden" id="destination_id" name="destination_id" value="{{$destination->id}}" />
    <input type="hidden" id="payment_id" name="payment_id" value="{{$payment->id}}" />
    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Destination</span>
            <input type="text" placeholder="destination" name="destination" readonly value="{{$destination->name}}" />

            <label
                for="destination"
                generated="true"
                class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Association</span>
            <select name="assoc" id="assoc_selection" readonly>
                <option value="{{ $association->id }}" selected>{{ $association->name }}</option>
            </select>
            <label
                for="assoc"
                generated="true"
                class="error"></label>
        </div>

        <div class="operator_data child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Operator </span>
            <select name="operator" id="operator_data" readonly>
                <option value="{{$tourOperator->id}}" selected>{{$tourOperator->name}}</option>
            </select>
            <label
                for="operator"
                generated="true"
                class="error"></label>
        </div>
        <div class="departure_data child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Departure</span>
            <select name="departure" id="departure_data" readonly>
                <option value="{{$departure->id}}" selected>{{$departure->date_range}}</option>
            </select>
            <label
                for="departure"
                generated="true"
                class="error"></label>
        </div>




        <div class="group_size child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Group Size</span>
            <input type="number" placeholder="Group size" value="{{$payment->total_tourist}}" name="size" readonly />
            <label
                for="size"
                generated="true"
                class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Entrance Fee (individual)</span>
            <input type="number" placeholder="Fee (per person)" name="individual_entrance_fee" value="{{$payment->entrance_fee}}" readonly />
            <label for="individual_entrance_fee" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Total (Group) Entrance Fee</span>
            <input type="number" placeholder="Fee (per person)" name="total_entrance_fee" readonly value="{{$payment->entrance_fee * $payment->total_tourist}}" />
            <label for="total_entrance_fee" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Group Fee</span>
            @php
            $groupFees = json_decode($payment->group_fee, true);
            // Check if groupFees is empty
            if (empty($groupFees) || !is_array($groupFees)) {
            $groupFeeDisplay = 'N/A';
            } else {
            $groupFeeDisplay = collect($groupFees)->map(function($value, $key) {
            return ucfirst($key) . ': ' . $value;
            })->implode(', ');
            }
            @endphp

            <input type="text" placeholder="Group Fee" class="tagify" name="group_fee" readonly value="{{ $groupFeeDisplay }}" />
            <label
                for="group_fee"
                generated="true"
                class="error"></label>
        </div>



        <div class="subTotal_amount child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Sub Total</span>
            <input type="number" placeholder="Sub total Amount" name="subTotal_amount" value="{{$payment->sub_total}}" id='subTotal_amount' readonly />
            <label
                for="subTotal_amount"
                generated="true"
                class="error"></label>
        </div>

        <div class="total_amount_vat child_field form_field position-relative col-12 col-md-6 col-lg-6 ">

            <span>{{$payment->tax_type}} ({{round($payment->tax_percentage)}} %)</span>
            <input type="number" placeholder="Total Amount Vat" name="total_amount_tax" value="{{$payment->tax}}" id='total_amount_tax' readonly />
            <label
                for="total_amount_tax"
                generated="true"
                class="error"></label>
        </div>


        <div class="total_amount child_field form_field position-relative col-12 col-md-6 col-lg-6 ">
            <span>Final Amount</span>
            <input type="number" placeholder="Total Amount" name="total_amount" value="{{$payment->total_payment}}" id='total_amount' readonly />
            <label
                for="total_amount"
                generated="true"
                class="error"></label>
        </div>

        <div class="total_amount child_field form_field position-relative col-12 col-md-6 col-lg-6 ">
            <span>Payment Date</span>
            <input type="date" placeholder="Payment Date" name="payment_date" value="{{ $payment->created_at->format('Y-m-d') }}" id='payment_date' readonly />
            <label
                for="payment_date"
                generated="true"
                class="error"></label>
        </div>



    </div>

    @include('dashboard/payments/paymentSlip')

</form>

@endsection