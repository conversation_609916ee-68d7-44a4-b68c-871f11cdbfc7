
<?php $__env->startSection('title', 'Payments'); ?>
<?php $__env->startSection('content'); ?>

<?php
$user = auth()->user();
?>
<div class="redirect_button d-flex w-100 justify-content-end">
    <?php if($user && $user->role && $user->role->role_key === 'destination_agent'): ?>
    <a href="/dashboard/destination/payment/add">Add new payment</a>
    <?php endif; ?>

</div>
<table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>

            <th>Tour Operator Image</th>
            <th>Tour Operator Name</th>
            <th>Departure Date</th>
            <th>Destination</th>
            <th>Total Tourist</th>
            <th>Entrance Fee (indivodual)</th>
            <th>Total Entrance Fee</th>
            <!-- <th>Group Fee Type</th> -->
            <th>Group Fee</th>
            <th>Sub Total</th>

            <th>Total Payment</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'destination_agent' || $user->role->role_key === 'tour_operator')): ?>
            <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </thead>
    <tbody>

        <?php if($payments->isNotEmpty()): ?>
        <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <tr>
            <td>
                <div class="table_image d-flex align-items-center justify-content-center">
                    <?php if(optional($payment->departure)->tourOperator && $payment->departure->tourOperator->profile_img): ?>
                    <div class="file_preview_hov w-100 h-100 d-flex flex-column align-items-center position-relative">
                        <img class="w-100 h-100 object-fit-contain" src="<?php echo e($payment->departure->tourOperator->profile_img); ?>" alt="Tour Operator">
                        <button class="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center" onclick="openPreview('<?php echo e($payment->departure->tourOperator->profile_img); ?>', 'image')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <?php else: ?>
                    <img class="w-100 h-100 object-fit-contain" src="/images/dummy.webp" alt="Default Image">
                    <?php endif; ?>
                </div>
            </td>
            <td><?php echo e(optional($payment->departure->tourOperator)->name ?? 'N/A'); ?></td>
            <td><?php echo e(optional($payment->departure)->date_range ?? 'N/A'); ?></td>

            <td><?php echo e($payment->destination->name); ?></td>
            <td><?php echo e($payment->total_tourist); ?></td>
            <td><?php echo e($payment->entrance_fee); ?></td>
            <td><?php echo e($payment->entrance_fee * $payment->total_tourist); ?></td>
            <!-- <td><?php echo e($payment->destination->fee_type); ?></td> -->
            <td>

                <?php
                $groupFees = json_decode($payment->group_fee, true);

                // Check if groupFees is empty
                if (empty($groupFees) || !is_array($groupFees)) {
                echo 'N/A';
                } else {
                $groupFeeDisplay = collect($groupFees)->map(function($value, $key) {
                return ucfirst($key) . ': ' . $value;
                })->implode(', ');
                echo $groupFeeDisplay;
                }
                ?>
            </td>

            <td><?php echo e($payment->sub_total); ?></td>

            <td><?php echo e($payment->total_payment); ?></td>
            <td><?php echo e($payment->created_at->format('d M Y')); ?></td>

            <?php if($user && $user->role && ($user->role->role_key === 'destination_agent' || $user->role->role_key === 'tour_operator')): ?>
            <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_<?php echo e($payment->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_<?php echo e($payment->id); ?>">
                        <ul class="action_dropdown d-grid w-100">
                            <li class="w-100">
                                <a href="<?php echo e(route('payment.update', $payment->id)); ?>" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i>
                                    <?php echo e($user && $user->role && ($user->role->role_key === 'destination_agent') ? 'Generate Slip' : 'Print Slip'); ?>

                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <?php endif; ?>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>

    </tbody>
    <tfoot>
        <tr>
            <th>Tour Operator Image</th>
            <th>Tour Operator Name</th>
            <th>Departure Date</th>
            <th>Destination</th>
            <th>Total Tourist</th>
            <th>Entrance Fee (indivodual)</th>
            <th>Total Entrance Fee</th>
            <!-- <th>Group Fee Type</th> -->
            <th>Group Fee</th>
            <th>Sub Total</th>

            <th>Total Payment</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'destination_agent' || $user->role->role_key === 'tour_operator')): ?>
            <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </tfoot>
</table>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\Tour Laravel Portal\yaredTourPortal\resources\views/dashboard/payments/index.blade.php ENDPATH**/ ?>