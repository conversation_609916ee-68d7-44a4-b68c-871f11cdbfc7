<?php

namespace App\Http\Controllers\Api;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Models\TourOperators;
use App\Models\Association;
use App\Models\Destinations;
use App\Models\DestinationAgents;
use App\Models\DestinationPrices;
use App\Models\Departures;
use App\Models\Payments;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class paymentController extends Controller
{

      
       public function paymentsList(Request $request)
       {
           $userId = $request->query('user_id');
   
           $user = User::find($userId);
   
           if (!$user || !$user->destinationAgent) {
               return response()->json(['error' => 'Destination agent not found for this user id.'], 401);
           }
   
           $destinationAgent = $user->destinationAgent;
           // Fetch payments with related departure and tour operator
           $payments = Payments::where('destination_id', $destinationAgent->destination_id)
               ->with(['departure.tourOperator','destination'])
               ->get();
     
           return response()->json([
               'payments' =>$payments
           ]);
       }

       
    public function getOperators(Request $request)
    {

        if (!$request->has('assoc_id')) {
            return response()->json([
                'status' => 400,
                'message' => 'Association ID is required.'
            ], 400);
        }

        $operators = TourOperators::where('assoc_id', $request->assoc_id)->get();
        if ($operators->isEmpty()) {
            return response()->json([
                'status' => 404,
                'message' => 'No operators found for this association.'
            ], 404);
        }

        return response()->json([
            'status' => 200,
            'data' => $operators
        ], 200);
    }

    public function getDepartures(Request $request)
    {

        if (!$request->has('operator_id')) {
            return response()->json([
                'status' => 400,
                'message' => 'Agent ID is required.'
            ], 400);
        }

        $departure = Departures::where('tour_operator_id', $request->operator_id)->where('status', 1)->get();
        if ($departure->isEmpty()) {
            return response()->json([
                'status' => 404,
                'message' => 'No departure found for this operator.'
            ], 404);
        }

        return response()->json([
            'status' => 200,
            'data' => $departure
        ], 200);
    }

    public function getDepartureDetail(Request $request)
    {
        $user = Auth::user();
        $agentDestinationId = $user->destinationAgent->destination->id;

        $departure = Departures::where('id', $request->departure_id)->first();

        // Decode the departure's destinations array
        $departureDestinations = json_decode($departure->destinations, true);

        // Check if the agent's destination ID exists in the departure's destinations
        if (!in_array($agentDestinationId, $departureDestinations)) {
            return response()->json(['errors' => 'This departure do not have the current destination in list'], 403);
        }

        // Fetch the destination pricing based on the agent's destination ID
        $pricingRecords = DestinationPrices::where('destination_id', $agentDestinationId)->get();
        $destinationRecord = Destinations::where('id', $agentDestinationId)->first();

        // Find the correct price based on the total_tourist count
        $selectedPrice = [];
        $priceRange = [];

        if ($destinationRecord->fee_type_status == '1') {
            // Group pricing records by fee_type
            $groupedByFeeType = $pricingRecords->groupBy('fee_type');

            foreach ($groupedByFeeType as $feeType => $records) {
                $matched = false;

                foreach ($records as $pricing) {
                    if (
                        $departure->total_tourist >= $pricing->min_group_size &&
                        $departure->total_tourist <= $pricing->max_group_size
                    ) {

                        $selectedPrice[$feeType] = $pricing->group_price;
                        $priceRange[$pricing->id] = $pricing;
                        $matched = true;
                        break; // Stop once matched for this fee_type
                    }
                }

                if (!$matched) {
                    return response()->json([
                        'errors' => "The group size does not match any price range for fee type '$feeType'."
                    ], 403);
                }
            }
        }

        // Calculate pricing details
        $individualEntranceFee = $destinationRecord->entrance_fee;
        $totalEntranceFee = $individualEntranceFee * $departure->total_tourist;

        $subTotal = array_sum($selectedPrice) + $totalEntranceFee;
        $tax = $destinationRecord->tax;

        $taxPercentage = 0;
        $taxAmount = 0;
        $totalWithTax = $subTotal;
        // Check if tax exists and has a percentage
        if ($tax && $tax->percentage > 0) {
            $taxPercentage = $tax->percentage;
            $taxAmount = ($subTotal * $taxPercentage) / 100;
            $totalWithTax = $subTotal + $taxAmount;
        }

        // Prepare response data
        $responseData = [
            'group_size' => $departure->total_tourist,
            'individual_entrance_fee' => $individualEntranceFee,
            'total_entrance_fee' => $totalEntranceFee,
            'sub_total' => $subTotal,
            'tax' => round($taxAmount),
            'total_price' => round($totalWithTax),
            'priceRange' => $priceRange, // Round total price to 2 decimal places
            'group_fee' => $selectedPrice // Round total price to 2 decimal places
        ];

        return response()->json(['status' => 200, 'data' => $responseData], 200);
    }

    // generate payment slip before proessing payment in system
    public function generatePaymentSlip(Request $request)
    {
        $user = Auth::user();
        if ($user->role->role_key == 'destination_agent') {
            $destinationAgent = $user->destinationAgent;
        }

        // Fetch the related data from database
        $departure = Departures::where('id', $request->departure_id)->first();
        $operator = TourOperators::where('id', $request->operator_id)->first();
        $assoc = Association::where('id', $request->assoc_id)->first();
        $destination = Destinations::where('id', $request->destination_id)->first();

        if ($user->role->role_key == 'tour_operator') {
            $destinationAgent = DestinationAgents::where('destination_id', $request->destination_id)->first();
        }



        // Ensure all records exist before proceeding
        if (!$departure || !$operator || !$assoc || !$destination) {
            return response()->json(['message' => 'Invalid data provided'], 400);
        }

        // Fetch the destination pricing based on the agent's destination ID
        $pricingRecords = DestinationPrices::where('destination_id', $destination->id)->get();

        
         // Find the correct price based on the total_tourist count
         $selectedPrice = [];
         $priceRange = [];
 
         if ($destination->fee_type_status == '1') {
             // Group pricing records by fee_type
             $groupedByFeeType = $pricingRecords->groupBy('fee_type');
 
             foreach ($groupedByFeeType as $feeType => $records) {
                 $matched = false;
 
                 foreach ($records as $pricing) {
                     if (
                         $departure->total_tourist >= $pricing->min_group_size &&
                         $departure->total_tourist <= $pricing->max_group_size
                     ) {
 
                         $selectedPrice[$feeType] = $pricing->group_price;
                         $priceRange[$pricing->id] = $pricing;
                         $matched = true;
                         break; // Stop once matched for this fee_type
                     }
                 }
 
                 if (!$matched) {
                     return response()->json([
                         'errors' => "The group size does not match any price range for fee type '$feeType'."
                     ], 403);
                 }
             }
         }
 

        // Calculate the total price
        $individualEntranceFee = $destination->entrance_fee;
        $totalEntranceFee = $individualEntranceFee * $departure->total_tourist;

        $subTotalPrice = array_sum($selectedPrice) + $totalEntranceFee;

        $tax = $destination->tax;

        $taxPercentage = 0;
        $taxAmount = 0;
        $totalWithTax = $subTotalPrice;
        // Check if tax exists and has a percentage
        if ($tax && $tax->percentage > 0) {
            $taxPercentage = $tax->percentage;
            $taxAmount = ($subTotalPrice * $taxPercentage) / 100;
            $totalWithTax = $subTotalPrice + $taxAmount;
        }

        // Return JSON response to update the form dynamically
        return response()->json([

            'destination_agent_name' => $destinationAgent->name,
            'assoc_name' => $assoc->name,
            'operator_name' => $operator->name,
            'tin_num' => $operator->tin_num,
            'group_name' => $departure->group_name,
            'nationality' => $departure->nationality,
            'destination_name' => $destination->name,
            'individual_entrance_fee' => $individualEntranceFee,
            'total_entrance_fee' => $totalEntranceFee,
            'group_size' => $departure->total_tourist,
            'group_fee_type' => $priceRange,
            'group_fee' => $selectedPrice,
            'subTotalPrice' => $subTotalPrice,
            'tax' => round($taxAmount),
            'tax_type' => $tax->name,
            'tax_percentage' => round($tax->percentage),
            'total_price' => round($totalWithTax),
            'departure_date' => $departure->date_range,
        ]);
    }

    public function destinationPayment(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'destination' => 'required',
            'assoc' => 'required',
            'operator' => 'required',
            'departure' => 'required',
            'size' => 'required',
            'individual_entrance_fee' => 'required',
            'subTotal_amount' => 'required',
            'total_amount' => 'required',

        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 403);
        }

        // Check if payment record already exists
        $existingPayment = Payments::where('departure_id', $request->departure)
            ->where('destination_id', $request->destination_id)
            ->first();

        if ($existingPayment) {
            return response()->json([
                'message' => 'Payment record already exists!',
                'status' => 'error'
            ], 409);
        }

        $departure = Departures::where('id', $request->departure)->first();

        $destination = Destinations::where('id', $request->destination_id)->first();

        // Fetch the destination pricing based on the agent's destination ID
        $pricingRecords = DestinationPrices::where('destination_id', $destination->id)->get();

        // Find the correct price based on the total_tourist count
        $selectedPrice = [];
        $priceRange = [];

        if ($destination->fee_type_status == '1') {
            // Group pricing records by fee_type
            $groupedByFeeType = $pricingRecords->groupBy('fee_type');

            foreach ($groupedByFeeType as $feeType => $records) {
                $matched = false;

                foreach ($records as $pricing) {
                    if (
                        $departure->total_tourist >= $pricing->min_group_size &&
                        $departure->total_tourist <= $pricing->max_group_size
                    ) {

                        $selectedPrice[$feeType] = $pricing->group_price;
                        $priceRange[$pricing->id] = $pricing;
                        $matched = true;
                        break; // Stop once matched for this fee_type
                    }
                }

                if (!$matched) {
                    return response()->json([
                        'errors' => "The group size does not match any price range for fee type '$feeType'."
                    ], 403);
                }
            }
        }

        // Calculate pricing details
        $individualEntranceFee = $destination->entrance_fee;
        $totalEntranceFee = $individualEntranceFee * $departure->total_tourist;
        $subTotalPrice = array_sum($selectedPrice) + $totalEntranceFee;

        $tax = $destination->tax;

        $taxPercentage = 0;
        $taxAmount = 0;
        $totalWithTax = $subTotalPrice;
        // Check if tax exists and has a percentage
        if ($tax && $tax->percentage > 0) {
            $taxPercentage = $tax->percentage;
            $taxAmount = ($subTotalPrice * $taxPercentage) / 100;
            $totalWithTax = $subTotalPrice + $taxAmount;
        }

        // Create the association record
        $payment = Payments::create([
            'destination_id' => $request->destination_id,
            'departure_id' => $request->departure,
            'entrance_fee' => $individualEntranceFee,
            'total_tourist' => $departure->total_tourist,
            'sub_total' => $subTotalPrice,
            'tax' => round($taxAmount),
            'tax_type' => $tax->name,
            'tax_percentage' => round($tax->percentage),
            'total_payment' => round($totalWithTax),
            'group_fee' => json_encode($selectedPrice),
            'serial_number' => $request->serialNumber,
        ]);

        return response()->json(['message' => 'payment created successfully!', "status" => "success"], 200);
    }

    // generate payment slip after proessing payment in system because prices of destinations may change any time 
    public function generatePaymentRecord(Request $request)
    {
        $user = Auth::user();
        if ($user->role->role_key == 'destination_agent') {
            $destinationAgent = $user->destinationAgent;
        }

        // Fetch the related data from database
        $payments = Payments::where('id', $request->payment_id)->first();
        $departure = Departures::where('id', $request->departure_id)->first();
        $operator = TourOperators::where('id', $request->operator_id)->first();
        $assoc = Association::where('id', $request->assoc_id)->first();
        $destination = Destinations::where('id', $request->destination_id)->first();

        if ($user->role->role_key == 'tour_operator') {
            $destinationAgent = DestinationAgents::where('destination_id', $request->destination_id)->first();
        }



        // Ensure all records exist before proceeding
        if (!$departure || !$operator || !$assoc || !$destination) {
            return response()->json(['message' => 'Invalid data provided'], 400);
        }


        // Return JSON response to update the form dynamically
        return response()->json([

            'serial_number' => $payments->serial_number,
            'destination_agent_name' => $destinationAgent->name,
            'assoc_name' => $assoc->name,
            'operator_name' => $operator->name,
            'tin_num' => $operator->tin_num,
            'group_name' => $departure->group_name,
            'nationality' => $departure->nationality,
            'destination_name' => $destination->name,
            'individual_entrance_fee' => $payments->entrance_fee,
            'total_entrance_fee' => $payments->entrance_fee * $payments->total_tourist,
            'group_size' => $payments->total_tourist,
           // 'group_fee_type' => $destination->fee_type,
            'group_fee' => $payments->group_fee,
            'subTotalPrice' => $payments->sub_total,
            'tax' => round($payments->tax),
            'tax_type' => $payments->tax_type,
            'tax_percentage' => round($payments->tax_percentage),
            'total_price' => round($payments->total_payment),
            'departure_date' => $departure->date_range,
            'created_at' => $payments->created_at->format('d M Y'),

        ]);
    }
}
